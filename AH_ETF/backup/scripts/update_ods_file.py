#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新ODS文件的专用脚本
将批量获取的ETF持仓数据写入到原始的CoreETFTopTenHoldings.ods文件中
"""

import pandas as pd
import os
import sys
from datetime import datetime
import traceback

def update_ods_with_holdings_data(ods_file_path, excel_data_file):
    """
    使用Excel数据更新ODS文件
    """
    try:
        print(f"开始更新ODS文件: {ods_file_path}")
        print(f"数据源文件: {excel_data_file}")
        
        # 读取数据源文件
        if excel_data_file.endswith('.xlsx'):
            df_data = pd.read_excel(excel_data_file, engine='openpyxl')
        else:
            df_data = pd.read_excel(excel_data_file, engine='odf')
        
        print(f"成功读取数据源文件，共{len(df_data)}行数据")
        
        # 创建备份
        backup_file = ods_file_path.replace('.ods', f'_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.ods')
        try:
            import shutil
            shutil.copy2(ods_file_path, backup_file)
            print(f"已创建备份文件: {backup_file}")
        except Exception as e:
            print(f"创建备份失败: {e}")
        
        # 尝试直接保存为ODS格式
        try:
            output_ods = ods_file_path.replace('.ods', '_updated_new.ods')
            df_data.to_excel(output_ods, index=False, engine='odf')
            print(f"✅ 成功创建新的ODS文件: {output_ods}")
            return output_ods
        except Exception as e:
            print(f"直接保存ODS失败: {e}")
            
        # 如果直接保存失败，尝试使用LibreOffice转换
        try:
            # 先保存为Excel
            temp_excel = ods_file_path.replace('.ods', '_temp.xlsx')
            df_data.to_excel(temp_excel, index=False, engine='openpyxl')
            print(f"临时Excel文件已创建: {temp_excel}")
            
            # 尝试使用LibreOffice转换
            output_ods = ods_file_path.replace('.ods', '_updated_new.ods')
            convert_cmd = f'libreoffice --headless --convert-to ods --outdir "{os.path.dirname(output_ods)}" "{temp_excel}"'
            
            import subprocess
            result = subprocess.run(convert_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                # 重命名转换后的文件
                converted_file = temp_excel.replace('.xlsx', '.ods')
                if os.path.exists(converted_file):
                    os.rename(converted_file, output_ods)
                    print(f"✅ 使用LibreOffice成功创建ODS文件: {output_ods}")
                    
                    # 清理临时文件
                    if os.path.exists(temp_excel):
                        os.remove(temp_excel)
                    
                    return output_ods
                else:
                    print("LibreOffice转换失败：找不到输出文件")
            else:
                print(f"LibreOffice转换失败: {result.stderr}")
                
        except Exception as e:
            print(f"LibreOffice转换失败: {e}")
        
        # 如果所有方法都失败，至少保留Excel文件
        excel_output = ods_file_path.replace('.ods', '_updated_new.xlsx')
        df_data.to_excel(excel_output, index=False, engine='openpyxl')
        print(f"⚠️  ODS转换失败，已保存为Excel格式: {excel_output}")
        return excel_output
        
    except Exception as e:
        print(f"❌ 更新ODS文件失败: {e}")
        traceback.print_exc()
        return None

def create_formatted_ods_manually(data_file, output_file):
    """
    手动创建格式化的ODS文件
    """
    try:
        from odf.opendocument import OpenDocumentSpreadsheet
        from odf.table import Table, TableRow, TableCell
        from odf.text import P
        
        print("尝试手动创建ODS文件...")
        
        # 读取数据
        if data_file.endswith('.xlsx'):
            df = pd.read_excel(data_file, engine='openpyxl')
        else:
            df = pd.read_excel(data_file, engine='odf')
        
        # 创建ODS文档
        doc = OpenDocumentSpreadsheet()
        table = Table(name="ETF持仓数据")
        
        # 添加表头
        header_row = TableRow()
        for col_name in df.columns:
            cell = TableCell()
            cell.addElement(P(text=str(col_name)))
            header_row.addElement(cell)
        table.addElement(header_row)
        
        # 添加数据行
        for index, row in df.iterrows():
            data_row = TableRow()
            for value in row:
                cell = TableCell()
                cell.addElement(P(text=str(value) if pd.notna(value) else ""))
                data_row.addElement(cell)
            table.addElement(data_row)
        
        doc.spreadsheet.addElement(table)
        doc.save(output_file)
        
        print(f"✅ 手动创建ODS文件成功: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"手动创建ODS文件失败: {e}")
        return None

def main():
    """主函数"""
    try:
        # 文件路径
        ods_file = "/home/<USER>/st/ETF/CoreETFTopTenHoldings.ods"
        excel_data_file = "/home/<USER>/st/ETF/CoreETFTopTenHoldings_updated.xlsx"
        
        if not os.path.exists(ods_file):
            print(f"❌ 原始ODS文件不存在: {ods_file}")
            return
            
        if not os.path.exists(excel_data_file):
            print(f"❌ 数据源文件不存在: {excel_data_file}")
            return
        
        print("开始更新ODS文件...")
        
        # 方法1：直接更新
        result = update_ods_with_holdings_data(ods_file, excel_data_file)
        
        if result and result.endswith('.ods'):
            print(f"✅ ODS文件更新成功: {result}")
        elif result:
            print(f"⚠️  已保存为Excel格式: {result}")
            
            # 方法2：尝试手动创建ODS
            manual_ods = ods_file.replace('.ods', '_manual_updated.ods')
            manual_result = create_formatted_ods_manually(result, manual_ods)
            
            if manual_result:
                print(f"✅ 手动创建ODS文件成功: {manual_result}")
            else:
                print("⚠️  手动创建ODS也失败，请使用Excel文件")
        else:
            print("❌ 所有更新方法都失败了")
            
        print("\n📋 文件说明:")
        print("- CoreETFTopTenHoldings.ods: 原始文件")
        print("- CoreETFTopTenHoldings_updated.xlsx: Excel格式的更新数据")
        print("- CoreETFTopTenHoldings_updated_new.ods: 新的ODS格式文件（如果成功创建）")
        print("- CoreETFTopTenHoldings_manual_updated.ods: 手动创建的ODS文件（如果成功创建）")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
