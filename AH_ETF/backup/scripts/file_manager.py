#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF项目文件管理工具
整理、归纳和管理项目中的各类文件
"""

import os
import shutil
from datetime import datetime
import glob
import json

class FileManager:
    def __init__(self, project_dir="."):
        self.project_dir = os.path.abspath(project_dir)
        self.file_categories = {
            "核心脚本": {
                "patterns": ["top10.py", "batch_etf_crawler.py", "format_converter.py", "update_ods_file.py", "demo.py", "file_manager.py"],
                "description": "主要功能脚本文件"
            },
            "配置文件": {
                "patterns": ["requirements.txt", "*.md"],
                "description": "配置和文档文件"
            },
            "输入文件": {
                "patterns": ["*ETF*.csv", "*ETF*.xlsx", "*ETF*.ods", "test_*.xlsx", "test_*.csv", "test_*.ods"],
                "exclude_patterns": ["*_updated.*", "*_backup_*", "*_converted.*"],
                "description": "原始ETF数据输入文件"
            },
            "输出文件": {
                "patterns": ["*_updated.*", "all_etf_holdings_*.txt", "etf_*_top10_*.txt"],
                "description": "脚本生成的结果文件"
            },
            "备份文件": {
                "patterns": ["*_backup_*"],
                "description": "自动生成的备份文件"
            },
            "转换文件": {
                "patterns": ["*_converted.*"],
                "description": "格式转换生成的文件"
            },
            "临时文件": {
                "patterns": ["__pycache__", "*.pyc", "*.pyo", "*.tmp", "*~"],
                "description": "临时和缓存文件"
            }
        }
    
    def scan_files(self):
        """扫描项目目录中的所有文件"""
        all_files = []
        
        # 获取所有文件
        for root, dirs, files in os.walk(self.project_dir):
            # 跳过隐藏目录
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if not file.startswith('.'):  # 跳过隐藏文件
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.project_dir)
                    file_size = os.path.getsize(file_path)
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    all_files.append({
                        'path': rel_path,
                        'full_path': file_path,
                        'name': file,
                        'size': file_size,
                        'mtime': file_mtime,
                        'category': self.categorize_file(rel_path)
                    })
        
        return sorted(all_files, key=lambda x: x['mtime'], reverse=True)
    
    def categorize_file(self, file_path):
        """根据文件路径和名称分类文件"""
        file_name = os.path.basename(file_path)
        
        for category, config in self.file_categories.items():
            # 检查排除模式
            if 'exclude_patterns' in config:
                excluded = False
                for exclude_pattern in config['exclude_patterns']:
                    if self.match_pattern(file_name, exclude_pattern):
                        excluded = True
                        break
                if excluded:
                    continue
            
            # 检查匹配模式
            for pattern in config['patterns']:
                if self.match_pattern(file_name, pattern) or self.match_pattern(file_path, pattern):
                    return category
        
        return "其他文件"
    
    def match_pattern(self, filename, pattern):
        """简单的模式匹配"""
        import fnmatch
        return fnmatch.fnmatch(filename, pattern)
    
    def generate_report(self):
        """生成文件分类报告"""
        files = self.scan_files()
        
        # 按类别分组
        categorized = {}
        for file_info in files:
            category = file_info['category']
            if category not in categorized:
                categorized[category] = []
            categorized[category].append(file_info)
        
        # 生成报告
        report = []
        report.append("=" * 80)
        report.append("ETF项目文件分类报告")
        report.append("=" * 80)
        report.append(f"扫描目录: {self.project_dir}")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"文件总数: {len(files)}")
        report.append("")
        
        # 统计信息
        total_size = sum(f['size'] for f in files)
        report.append(f"总文件大小: {self.format_size(total_size)}")
        report.append("")
        
        # 按类别显示
        for category in self.file_categories.keys():
            if category in categorized:
                files_in_category = categorized[category]
                category_size = sum(f['size'] for f in files_in_category)
                
                report.append(f"📁 {category} ({len(files_in_category)}个文件, {self.format_size(category_size)})")
                report.append("-" * 60)
                report.append(f"说明: {self.file_categories[category]['description']}")
                report.append("")
                
                for file_info in sorted(files_in_category, key=lambda x: x['mtime'], reverse=True):
                    report.append(f"  📄 {file_info['name']}")
                    report.append(f"      路径: {file_info['path']}")
                    report.append(f"      大小: {self.format_size(file_info['size'])}")
                    report.append(f"      修改: {file_info['mtime'].strftime('%Y-%m-%d %H:%M:%S')}")
                    report.append("")
                
                report.append("")
        
        # 其他文件
        if "其他文件" in categorized:
            files_in_category = categorized["其他文件"]
            category_size = sum(f['size'] for f in files_in_category)
            
            report.append(f"📁 其他文件 ({len(files_in_category)}个文件, {self.format_size(category_size)})")
            report.append("-" * 60)
            
            for file_info in sorted(files_in_category, key=lambda x: x['mtime'], reverse=True):
                report.append(f"  📄 {file_info['name']}")
                report.append(f"      路径: {file_info['path']}")
                report.append(f"      大小: {self.format_size(file_info['size'])}")
                report.append(f"      修改: {file_info['mtime'].strftime('%Y-%m-%d %H:%M:%S')}")
                report.append("")
        
        return "\n".join(report)
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def organize_files(self, create_dirs=True):
        """整理文件到不同目录"""
        if not create_dirs:
            print("预览模式 - 不会实际移动文件")
        
        files = self.scan_files()
        
        # 创建目录结构
        dirs_to_create = {
            "scripts": "核心脚本",
            "docs": "配置文件", 
            "input": "输入文件",
            "output": "输出文件",
            "backup": "备份文件",
            "temp": "临时文件"
        }
        
        if create_dirs:
            for dir_name in dirs_to_create.keys():
                dir_path = os.path.join(self.project_dir, dir_name)
                os.makedirs(dir_path, exist_ok=True)
        
        # 移动文件
        move_plan = []
        for file_info in files:
            category = file_info['category']
            source_path = file_info['full_path']
            
            # 确定目标目录
            target_dir = None
            if category == "核心脚本":
                target_dir = "scripts"
            elif category == "配置文件":
                target_dir = "docs"
            elif category == "输入文件":
                target_dir = "input"
            elif category == "输出文件":
                target_dir = "output"
            elif category == "备份文件":
                target_dir = "backup"
            elif category in ["临时文件", "转换文件"]:
                target_dir = "temp"
            
            if target_dir:
                target_path = os.path.join(self.project_dir, target_dir, file_info['name'])
                move_plan.append((source_path, target_path, category))
        
        # 显示移动计划
        print(f"文件整理计划 ({'实际执行' if create_dirs else '预览模式'}):")
        print("=" * 60)
        
        for source, target, category in move_plan:
            rel_source = os.path.relpath(source, self.project_dir)
            rel_target = os.path.relpath(target, self.project_dir)
            print(f"[{category}] {rel_source} -> {rel_target}")
            
            if create_dirs:
                try:
                    shutil.move(source, target)
                    print(f"  ✅ 移动成功")
                except Exception as e:
                    print(f"  ❌ 移动失败: {e}")
        
        return len(move_plan)
    
    def save_report(self, filename=None):
        """保存报告到文件"""
        if not filename:
            filename = f"file_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        report = self.generate_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 文件报告已保存: {filename}")
        return filename

def main():
    """主函数"""
    import sys
    
    manager = FileManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "report":
            # 生成并显示报告
            report = manager.generate_report()
            print(report)
            
        elif command == "save":
            # 保存报告到文件
            filename = manager.save_report()
            
        elif command == "organize":
            # 整理文件
            confirm = input("确定要整理文件吗？这会移动文件到不同目录 (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                count = manager.organize_files(create_dirs=True)
                print(f"✅ 已整理 {count} 个文件")
            else:
                print("❌ 已取消整理")
                
        elif command == "preview":
            # 预览整理计划
            count = manager.organize_files(create_dirs=False)
            print(f"计划整理 {count} 个文件")
            
        else:
            print("❌ 未知命令")
            print("可用命令: report, save, organize, preview")
    else:
        # 默认显示报告
        report = manager.generate_report()
        print(report)

if __name__ == "__main__":
    main()
