#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多工作表按顺序处理功能
"""

import pandas as pd
import os
import sys

def test_multi_sheet_processing():
    """测试多工作表处理功能"""
    
    # 测试文件路径
    test_file = "input/核心ETF十大持仓 (1).xlsx"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📋 测试文件: {test_file}")
    
    try:
        # 读取所有工作表
        all_sheets = pd.read_excel(test_file, sheet_name=None, engine='openpyxl')
        sheet_names = list(all_sheets.keys())
        
        print(f"发现 {len(sheet_names)} 个工作表:")
        for i, sheet_name in enumerate(sheet_names, 1):
            df = all_sheets[sheet_name]
            print(f"  {i}. {sheet_name}: {len(df)}行数据")
        
        # 指定要处理的工作表（按您的要求）
        target_sheets = ["宽基指数ETF", "境外ETF", "债券ETF", "商品和货币ETF"]
        
        print(f"\n🎯 按顺序处理工作表:")
        
        # 按工作表顺序处理
        sheet_etf_mapping = {}
        all_etf_codes = []
        
        for sheet_name in target_sheets:
            if sheet_name in all_sheets:
                df = all_sheets[sheet_name]
                print(f"\n📋 处理工作表: {sheet_name}")
                
                # 按表格顺序提取ETF代码
                etf_codes = []
                for index, row in df.iterrows():
                    for column in df.columns:
                        value = row[column]
                        if pd.notna(value):
                            str_value = str(value).strip()
                            # 检查是否是6位数字的ETF代码
                            if str_value.isdigit() and len(str_value) == 6:
                                if str_value not in etf_codes:
                                    etf_codes.append(str_value)
                                break  # 找到ETF代码后跳出列循环
                
                if etf_codes:
                    print(f"   提取到 {len(etf_codes)} 个ETF代码（按表格顺序）:")
                    for i, code in enumerate(etf_codes[:5], 1):  # 只显示前5个
                        print(f"     {i}. {code}")
                    if len(etf_codes) > 5:
                        print(f"     ... 还有 {len(etf_codes) - 5} 个")
                    
                    sheet_etf_mapping[sheet_name] = etf_codes
                    all_etf_codes.extend(etf_codes)
                else:
                    print(f"   ⚠️  未找到ETF代码")
        
        print(f"\n📊 总结:")
        print(f"处理了 {len(sheet_etf_mapping)} 个工作表")
        print(f"总共提取到 {len(all_etf_codes)} 个ETF代码")
        
        # 显示每个工作表的ETF数量
        for sheet_name, etf_codes in sheet_etf_mapping.items():
            print(f"  {sheet_name}: {len(etf_codes)} 个ETF")
        
        # 模拟按工作表顺序处理
        print(f"\n🔄 模拟按工作表顺序处理:")
        total_processed = 0
        
        for sheet_name in target_sheets:
            if sheet_name in sheet_etf_mapping:
                etf_codes = sheet_etf_mapping[sheet_name]
                print(f"\n[{sheet_name}] 处理 {len(etf_codes)} 个ETF:")
                
                for i, etf_code in enumerate(etf_codes, 1):
                    total_processed += 1
                    print(f"  第{i}/{len(etf_codes)}个: {etf_code} (总进度: {total_processed}/{len(all_etf_codes)})")
                    
                    # 这里可以调用实际的ETF数据获取函数
                    # holdings_data = get_etf_top_holdings(etf_code)
                    
                    if i >= 3:  # 只显示前3个作为演示
                        if len(etf_codes) > 3:
                            print(f"  ... 还有 {len(etf_codes) - 3} 个ETF待处理")
                        break
        
        print(f"\n✅ 测试完成！")
        print(f"验证了按工作表顺序、按表格顺序处理ETF的功能")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_multi_sheet_processing()
