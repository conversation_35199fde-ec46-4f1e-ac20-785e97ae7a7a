#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量获取所有ETF最新季度持仓前十大股票数据脚本
从CoreETFTopTenHoldings.ods文件读取ETF列表，批量获取数据并更新到文件中
"""

import requests
from datetime import datetime
import re
from bs4 import BeautifulSoup
import sys
import os
import time
import pandas as pd
from openpyxl import load_workbook
import traceback

# 导入原有的ETFCrawler类
from top10 import ETFCrawler

class BatchETFCrawler:
    def __init__(self, input_file_path, sheet_names=None):
        self.input_file_path = input_file_path
        self.file_extension = os.path.splitext(input_file_path)[1].lower()
        self.sheet_names = sheet_names if sheet_names else []  # 支持多个工作表
        self.etf_list = []
        self.results = {}
        self.all_sheets_data = {}  # 存储所有工作表的数据
        self.processed_sheets = []  # 记录已处理的工作表
        self.sheet_etf_mapping = {}  # 记录每个工作表对应的ETF代码和结果

    def read_etf_list_from_file(self):
        """从文件读取ETF代码列表，支持CSV、ODS、XLSX格式和多工作表"""
        try:
            print(f"正在读取文件: {self.input_file_path}")
            print(f"文件格式: {self.file_extension}")
            if self.sheet_names:
                print(f"指定工作表: {', '.join(self.sheet_names)}")

            all_etf_codes = []

            # 根据文件扩展名选择读取方法
            if self.file_extension == '.csv':
                try:
                    # CSV文件不支持多工作表
                    if self.sheet_names:
                        print("⚠️  CSV文件不支持工作表，忽略sheet_names参数")

                    # 尝试不同的编码格式读取CSV
                    for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                        try:
                            df = pd.read_csv(self.input_file_path, encoding=encoding)
                            print(f"成功使用{encoding}编码读取CSV文件，共{len(df)}行数据")
                            break
                        except UnicodeDecodeError:
                            continue
                    if df is None:
                        raise Exception("无法使用常见编码读取CSV文件")

                    # 从CSV提取ETF代码
                    etf_codes = self.extract_etf_codes_from_dataframe(df)
                    all_etf_codes.extend(etf_codes)

                except Exception as e:
                    print(f"读取CSV文件失败: {e}")
                    return self.extract_etf_codes_from_content()

            elif self.file_extension in ['.xlsx', '.ods']:
                try:
                    # 读取Excel/ODS文件，支持多工作表
                    engine = 'openpyxl' if self.file_extension == '.xlsx' else 'odf'

                    # 读取所有工作表
                    all_sheets = pd.read_excel(self.input_file_path, sheet_name=None, engine=engine)
                    sheet_names = list(all_sheets.keys())
                    print(f"发现{len(sheet_names)}个工作表: {', '.join(sheet_names)}")

                    # 保存所有工作表数据
                    self.all_sheets_data = all_sheets

                    # 确定要处理的工作表
                    if self.sheet_names:
                        # 使用指定的工作表
                        sheets_to_process = []
                        for sheet_name in self.sheet_names:
                            if sheet_name in all_sheets:
                                sheets_to_process.append(sheet_name)
                                print(f"✅ 找到工作表: {sheet_name}")
                            else:
                                print(f"❌ 未找到工作表: {sheet_name}")

                        if not sheets_to_process:
                            print("❌ 没有找到任何指定的工作表")
                            return []
                    else:
                        # 使用所有工作表
                        sheets_to_process = sheet_names
                        print(f"将处理所有工作表: {', '.join(sheets_to_process)}")

                    # 按工作表顺序处理每个工作表
                    for sheet_name in sheets_to_process:
                        df = all_sheets[sheet_name]
                        print(f"\n📋 处理工作表: {sheet_name} ({len(df)}行数据)")

                        # 从工作表提取ETF代码（保持表格顺序）
                        etf_codes = self.extract_etf_codes_from_dataframe_ordered(df)
                        if etf_codes:
                            print(f"   从 {sheet_name} 提取到 {len(etf_codes)} 个ETF代码")
                            # 记录每个工作表对应的ETF代码
                            self.sheet_etf_mapping[sheet_name] = etf_codes
                            all_etf_codes.extend(etf_codes)
                            self.processed_sheets.append(sheet_name)
                        else:
                            print(f"   ⚠️  {sheet_name} 中未找到ETF代码")

                except Exception as e:
                    print(f"读取{self.file_extension.upper()}文件失败: {e}")
                    return self.extract_etf_codes_from_content()
            else:
                print(f"不支持的文件格式: {self.file_extension}")
                print("支持的格式: .csv, .xlsx, .ods")
                return []
            
            # 查找包含ETF代码的列
            etf_codes = []
            for column in df.columns:
                for index, value in df[column].items():
                    if pd.notna(value):
                        # 尝试提取6位数字的ETF代码
                        str_value = str(value).strip()
                        if re.match(r'^\d{6}$', str_value):
                            etf_codes.append(str_value)
                        else:
                            # 从文本中提取6位数字
                            matches = re.findall(r'\b\d{6}\b', str_value)
                            etf_codes.extend(matches)
            
            # 去重并排序
            etf_codes = list(set(etf_codes))
            etf_codes.sort()
            
            print(f"总共提取到{len(all_etf_codes)}个ETF代码")
            return list(set(all_etf_codes))  # 去重

        except Exception as e:
            print(f"读取文件失败: {e}")
            print("尝试使用备用方法...")
            return self.extract_etf_codes_from_content()

    def extract_etf_codes_from_dataframe(self, df):
        """从DataFrame中提取ETF代码"""
        etf_codes = []

        # 查找包含ETF代码的列
        for column in df.columns:
            for _, value in df[column].items():
                if pd.notna(value):
                    # 尝试提取6位数字的ETF代码
                    str_value = str(value).strip()
                    if re.match(r'^\d{6}$', str_value):
                        etf_codes.append(str_value)
                    else:
                        # 从文本中提取6位数字
                        matches = re.findall(r'\b\d{6}\b', str_value)
                        etf_codes.extend(matches)

        # 去重并排序
        unique_codes = list(set(etf_codes))
        unique_codes.sort()
        return unique_codes

    def extract_etf_codes_from_dataframe_ordered(self, df):
        """从DataFrame中按表格顺序提取ETF代码（保持行顺序）"""
        etf_codes = []

        # 按行顺序查找ETF代码
        for index, row in df.iterrows():
            for column in df.columns:
                value = row[column]
                if pd.notna(value):
                    # 尝试提取6位数字的ETF代码
                    str_value = str(value).strip()
                    if re.match(r'^\d{6}$', str_value):
                        if str_value not in etf_codes:  # 避免重复但保持顺序
                            etf_codes.append(str_value)
                        break  # 找到ETF代码后跳出列循环，继续下一行
                    else:
                        # 从文本中提取6位数字
                        matches = re.findall(r'\b\d{6}\b', str_value)
                        for match in matches:
                            if match not in etf_codes:
                                etf_codes.append(match)
                        if matches:  # 如果找到了匹配，跳出列循环
                            break

        return etf_codes

    def extract_etf_codes_from_content(self):
        """从文件内容中提取ETF代码的备用方法"""
        try:
            # 读取文件的原始内容并尝试提取数字
            with open(self.ods_file_path, 'rb') as f:
                content = f.read()
            
            # 将二进制内容转换为字符串（忽略错误）
            text_content = content.decode('utf-8', errors='ignore')
            
            # 使用正则表达式查找6位数字
            etf_codes = re.findall(r'\b\d{6}\b', text_content)
            
            # 去重并过滤有效的ETF代码
            valid_codes = []
            for code in set(etf_codes):
                # 简单验证：ETF代码通常以1、5、6开头
                if code.startswith(('1', '5', '6')):
                    valid_codes.append(code)
            
            valid_codes.sort()
            print(f"使用备用方法提取到{len(valid_codes)}个可能的ETF代码")
            return valid_codes
            
        except Exception as e:
            print(f"备用方法也失败了: {e}")
            # 返回一些常见的ETF代码作为示例
            return ['510050', '510300', '159915', '159919', '512100', '515260', '562500']
    
    def get_all_etf_holdings(self):
        """按工作表顺序批量获取所有ETF的持仓数据"""
        self.etf_list = self.read_etf_list_from_file()

        if not self.etf_list:
            print("❌ 未能获取到ETF代码列表")
            return False

        print(f"开始按工作表顺序批量获取{len(self.etf_list)}个ETF的持仓数据...")

        success_count = 0
        failed_count = 0
        total_processed = 0

        # 按工作表顺序处理
        for sheet_name in self.processed_sheets:
            if sheet_name in self.sheet_etf_mapping:
                etf_codes = self.sheet_etf_mapping[sheet_name]
                print(f"\n🔄 处理工作表: {sheet_name} ({len(etf_codes)}个ETF)")
                print(f"ETF代码: {', '.join(etf_codes)}")

                # 为每个工作表初始化结果字典
                if sheet_name not in self.results:
                    self.results[sheet_name] = {}

                # 按表格顺序处理该工作表的ETF
                for i, etf_code in enumerate(etf_codes, 1):
                    total_processed += 1
                    print(f"\n{'='*60}")
                    print(f"[{sheet_name}] 第{i}/{len(etf_codes)}个ETF: {etf_code} (总进度: {total_processed}/{len(self.etf_list)})")
                    print(f"{'='*60}")

                    try:
                        # 创建ETF爬虫实例
                        crawler = ETFCrawler(etf_code)

                        # 获取持仓数据
                        holdings = crawler.get_top_holdings()

                        if holdings:
                            self.results[sheet_name][etf_code] = {
                                'etf_name': crawler.etf_name,
                                'holdings': holdings,
                                'status': 'success',
                                'update_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }
                            success_count += 1
                            print(f"✅ 成功获取ETF {etf_code} 的持仓数据")
                        else:
                            self.results[sheet_name][etf_code] = {
                                'etf_name': f'ETF({etf_code})',
                                'holdings': [],
                                'status': 'failed',
                                'update_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }
                            failed_count += 1
                            print(f"❌ 无法获取ETF {etf_code} 的持仓数据")

                        # 添加延迟避免请求过于频繁
                        if total_processed < len(self.etf_list):
                            print("等待3秒后继续...")
                            time.sleep(3)

                    except Exception as e:
                        print(f"❌ 处理ETF {etf_code} 时出错: {e}")
                        self.results[sheet_name][etf_code] = {
                            'etf_name': f'ETF({etf_code})',
                            'holdings': [],
                            'status': 'error',
                            'error': str(e),
                            'update_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        }
                        failed_count += 1

        print(f"\n{'='*80}")
        print(f"批量获取完成！")
        print(f"成功: {success_count}个, 失败: {failed_count}个, 总计: {len(self.etf_list)}个")
        print(f"处理了 {len(self.processed_sheets)} 个工作表")
        print(f"{'='*80}")

        return True
    
    def save_results_to_text(self, filename=None):
        """将结果保存到文本文件"""
        try:
            if not filename:
                filename = f"all_etf_holdings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=" * 100 + "\n")
                f.write("所有ETF最新季度持仓前十大股票数据汇总\n")
                f.write("=" * 100 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"ETF总数: {len(self.results)}\n")
                f.write("=" * 100 + "\n\n")
                
                for etf_code, data in self.results.items():
                    f.write(f"ETF代码: {etf_code}\n")
                    f.write(f"ETF名称: {data['etf_name']}\n")
                    f.write(f"状态: {data['status']}\n")
                    f.write(f"更新时间: {data['update_time']}\n")
                    
                    if data['status'] == 'success' and data['holdings']:
                        f.write("前十大持仓:\n")
                        for holding in data['holdings']:
                            rank = holding.get('rank', 'N/A')
                            code = holding.get('stock_code', 'N/A')
                            name = holding.get('stock_name', 'N/A')
                            ratio = holding.get('holding_ratio', 'N/A')
                            f.write(f"  {rank}. {name}({code}) - {ratio}\n")
                    else:
                        f.write("持仓数据: 获取失败\n")
                        if 'error' in data:
                            f.write(f"错误信息: {data['error']}\n")
                    
                    f.write("-" * 80 + "\n\n")
            
            print(f"结果已保存到文件: {filename}")
            return filename
            
        except Exception as e:
            print(f"保存文件失败: {e}")
            return None
    
    def update_output_files(self):
        """更新输出文件，支持多种格式和多工作表"""
        try:
            print("正在生成输出文件...")

            # 准备数据
            data_rows = []
            for etf_code, data in self.results.items():
                if data['status'] == 'success' and data['holdings']:
                    # 将前十大持仓组合成一个字符串
                    holdings_text = ""
                    for i, holding in enumerate(data['holdings']):
                        name = holding.get('stock_name', '')
                        ratio = holding.get('holding_ratio', '')
                        if name:
                            if ratio:
                                holdings_text += f"{name}({ratio})"
                            else:
                                holdings_text += name
                            if i < len(data['holdings']) - 1:
                                holdings_text += "、"

                    data_rows.append({
                        '代码': etf_code,
                        '名称': data['etf_name'],
                        '十大持仓': holdings_text,
                        '更新时间': data['update_time'],
                        '状态': '成功'
                    })
                else:
                    data_rows.append({
                        '代码': etf_code,
                        '名称': data['etf_name'],
                        '十大持仓': '获取失败',
                        '更新时间': data['update_time'],
                        '状态': '失败'
                    })

            # 创建DataFrame
            updated_df = pd.DataFrame(data_rows)

            # 生成基础文件名（去掉扩展名）
            base_filename = os.path.splitext(self.input_file_path)[0] + '_updated'

            output_files = []

            # 1. 保存为CSV文件
            try:
                csv_filename = base_filename + '.csv'
                updated_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"✅ 数据已保存到CSV文件: {csv_filename}")
                output_files.append(csv_filename)
            except Exception as e:
                print(f"❌ 保存CSV文件失败: {e}")

            # 2. 保存为Excel文件（支持多工作表）
            try:
                excel_filename = base_filename + '.xlsx'

                if self.all_sheets_data and self.file_extension == '.xlsx':
                    # 如果原文件有多个工作表，保持多工作表结构
                    print(f"   保持多工作表结构，共{len(self.all_sheets_data)}个工作表")
                    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                        for sheet_name, sheet_df in self.all_sheets_data.items():
                            if sheet_name in self.processed_sheets:
                                # 更新已处理的工作表的数据
                                print(f"   正在更新工作表: {sheet_name}")
                                merged_df = sheet_df.copy()

                                # 确保有十大持仓列
                                if '十大持仓' not in merged_df.columns:
                                    merged_df['十大持仓'] = ''

                                # 更新持仓数据
                                updated_count = 0
                                for _, row in updated_df.iterrows():
                                    etf_code = int(row['代码']) if str(row['代码']).isdigit() else row['代码']
                                    holdings = row['十大持仓']

                                    # 找到对应的ETF代码行并更新
                                    mask = merged_df['代码'] == etf_code
                                    if mask.any():
                                        merged_df.loc[mask, '十大持仓'] = holdings
                                        updated_count += 1

                                print(f"     更新了 {updated_count} 个ETF的持仓数据")
                                merged_df.to_excel(writer, sheet_name=sheet_name, index=False)
                            else:
                                # 保持其他工作表不变
                                print(f"   保持工作表不变: {sheet_name}")
                                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"✅ 数据已保存到Excel文件（多工作表）: {excel_filename}")
                    print(f"   更新了工作表: {', '.join(self.processed_sheets)}")
                else:
                    # 单工作表
                    updated_df.to_excel(excel_filename, index=False, engine='openpyxl')
                    print(f"✅ 数据已保存到Excel文件: {excel_filename}")

                output_files.append(excel_filename)
            except Exception as e:
                print(f"❌ 保存Excel文件失败: {e}")

            # 3. 保存为ODS文件（支持多工作表）
            try:
                ods_filename = base_filename + '.ods'

                if self.all_sheets_data and self.file_extension == '.ods':
                    # 如果原文件有多个工作表，保持多工作表结构
                    with pd.ExcelWriter(ods_filename, engine='odf') as writer:
                        for sheet_name, sheet_df in self.all_sheets_data.items():
                            if sheet_name in self.processed_sheets:
                                # 更新已处理的工作表的数据
                                merged_df = sheet_df.copy()

                                # 确保有十大持仓列
                                if '十大持仓' not in merged_df.columns:
                                    merged_df['十大持仓'] = ''

                                # 更新持仓数据
                                for _, row in updated_df.iterrows():
                                    etf_code = int(row['代码']) if str(row['代码']).isdigit() else row['代码']
                                    holdings = row['十大持仓']
                                    mask = merged_df['代码'] == etf_code
                                    if mask.any():
                                        merged_df.loc[mask, '十大持仓'] = holdings

                                merged_df.to_excel(writer, sheet_name=sheet_name, index=False)
                            else:
                                # 保持其他工作表不变
                                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"✅ 数据已保存到ODS文件（多工作表）: {ods_filename}")
                    print(f"   更新了工作表: {', '.join(self.processed_sheets)}")
                else:
                    # 单工作表
                    updated_df.to_excel(ods_filename, index=False, engine='odf')
                    print(f"✅ 数据已保存到ODS文件: {ods_filename}")

                output_files.append(ods_filename)
            except Exception as e:
                print(f"❌ 保存ODS文件失败: {e}")

            return output_files

        except Exception as e:
            print(f"❌ 生成输出文件失败: {e}")
            return []


def main():
    """主函数"""
    try:
        # 支持的文件格式
        supported_extensions = ['.csv', '.xlsx', '.ods']

        # 解析命令行参数
        input_file = None
        sheet_names = []

        if len(sys.argv) > 1:
            input_file = sys.argv[1]
            if not os.path.exists(input_file):
                print(f"❌ 指定的文件不存在: {input_file}")
                return

            # 检查是否指定了工作表名称
            if len(sys.argv) > 2:
                # 支持多个工作表名称，用逗号分隔或多个参数
                if ',' in sys.argv[2]:
                    sheet_names = [s.strip() for s in sys.argv[2].split(',')]
                else:
                    # 收集所有后续参数作为工作表名称
                    sheet_names = sys.argv[2:]
                print(f"指定工作表: {', '.join(sheet_names)}")
            else:
                # 如果没有指定工作表，使用默认的工作表列表
                sheet_names = ["宽基指数ETF", "境外ETF", "债券ETF", "商品和货币ETF"]
                print(f"使用默认工作表: {', '.join(sheet_names)}")
        else:
            # 自动查找文件
            possible_files = [
                "input/核心ETF十大持仓 (1).xlsx",
                "input/核心ETF十大持仓 (1).ods",
                "CoreETFTopTenHoldings.xlsx",
                "CoreETFTopTenHoldings.ods",
                "CoreETFTopTenHoldings.csv"
            ]

            for file_path in possible_files:
                if os.path.exists(file_path):
                    input_file = file_path
                    break

        if not input_file:
            print("❌ 未找到输入文件")
            print("请确保以下文件之一存在:")
            for file_path in ["input/核心ETF十大持仓 (1).xlsx",
                             "CoreETFTopTenHoldings.xlsx",
                             "CoreETFTopTenHoldings.csv"]:
                print(f"  - {file_path}")
            print("\n或者使用命令行参数指定文件:")
            print("  python3 batch_etf_crawler.py <文件路径> [工作表名称...]")
            print("\n示例:")
            print("  python3 batch_etf_crawler.py data.xlsx")
            print("  python3 batch_etf_crawler.py data.xlsx 宽基指数ETF 境外ETF")
            print("  python3 batch_etf_crawler.py data.xlsx \"宽基指数ETF,境外ETF,债券ETF\"")
            return

        # 检查文件格式
        file_ext = os.path.splitext(input_file)[1].lower()
        if file_ext not in supported_extensions:
            print(f"❌ 不支持的文件格式: {file_ext}")
            print(f"支持的格式: {', '.join(supported_extensions)}")
            return

        print("开始批量获取ETF持仓数据...")
        print(f"输入文件: {input_file}")
        print(f"文件格式: {file_ext}")
        if sheet_names:
            print(f"指定工作表: {', '.join(sheet_names)}")

        # 创建批量爬虫实例
        batch_crawler = BatchETFCrawler(input_file, sheet_names)

        # 获取所有ETF的持仓数据
        success = batch_crawler.get_all_etf_holdings()

        if success and batch_crawler.results:
            # 保存结果到文本文件
            text_file = batch_crawler.save_results_to_text()

            # 生成多格式输出文件
            output_files = batch_crawler.update_output_files()

            print(f"\n✅ 批量获取完成！")
            if text_file:
                print(f"📄 文本汇总报告: {text_file}")

            if output_files:
                print("📊 生成的数据文件:")
                for file_path in output_files:
                    file_size = os.path.getsize(file_path)
                    print(f"  - {file_path} ({file_size:,} 字节)")
            else:
                print("⚠️  未能生成数据文件")
        else:
            print("\n❌ 批量获取失败")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
