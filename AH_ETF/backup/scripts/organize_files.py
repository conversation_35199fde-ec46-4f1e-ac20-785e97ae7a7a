#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF项目文件整理脚本
将不同类型的文件整理到对应的文件夹中
"""

import os
import shutil
from datetime import datetime
import glob
import sys

class FileOrganizer:
    def __init__(self, project_dir="."):
        self.project_dir = os.path.abspath(project_dir)
        
        # 定义文件夹结构和文件分类规则
        self.folder_structure = {
            "scripts": {
                "name": "脚本文件",
                "patterns": [
                    "*.py"
                ],
                "exclude_patterns": [],
                "description": "Python脚本文件"
            },
            "docs": {
                "name": "文档配置",
                "patterns": [
                    "*.md",
                    "*.txt",
                    "requirements.txt"
                ],
                "exclude_patterns": [
                    "all_etf_holdings_*.txt",
                    "etf_*_top10_*.txt",
                    "*_report_*.txt"
                ],
                "description": "文档和配置文件"
            },
            "input": {
                "name": "输入数据",
                "patterns": [
                    "*ETF*.csv",
                    "*ETF*.xlsx", 
                    "*ETF*.ods",
                    "核心*.csv",
                    "核心*.xlsx",
                    "核心*.ods"
                ],
                "exclude_patterns": [
                    "*_updated.*",
                    "*_backup_*",
                    "*_converted.*"
                ],
                "description": "原始ETF数据文件"
            },
            "output": {
                "name": "输出结果",
                "patterns": [
                    "*_updated.csv",
                    "*_updated.xlsx",
                    "*_updated.ods",
                    "all_etf_holdings_*.txt",
                    "etf_*_top10_*.txt"
                ],
                "exclude_patterns": [],
                "description": "脚本生成的结果文件"
            },
            "backup": {
                "name": "备份文件",
                "patterns": [
                    "*_backup_*.*"
                ],
                "exclude_patterns": [],
                "description": "自动生成的备份文件"
            },
            "reports": {
                "name": "报告文件",
                "patterns": [
                    "*_report_*.txt",
                    "file_report_*.txt",
                    "cleanup_report_*.txt"
                ],
                "exclude_patterns": [],
                "description": "管理和分析报告"
            },
            "temp": {
                "name": "临时文件",
                "patterns": [
                    "__pycache__",
                    "*.pyc",
                    "*.pyo",
                    "*.tmp",
                    "*~",
                    ".DS_Store",
                    "*_converted.*"
                ],
                "exclude_patterns": [],
                "description": "临时和缓存文件"
            },
            "test": {
                "name": "测试文件",
                "patterns": [
                    "test_*.*",
                    "multi_sheet_test.*"
                ],
                "exclude_patterns": [],
                "description": "测试用的文件"
            }
        }
    
    def match_pattern(self, filename, pattern):
        """简单的模式匹配"""
        import fnmatch
        return fnmatch.fnmatch(filename, pattern)
    
    def categorize_file(self, file_path):
        """根据文件路径和名称分类文件"""
        file_name = os.path.basename(file_path)
        
        for folder, config in self.folder_structure.items():
            # 检查排除模式
            excluded = False
            for exclude_pattern in config['exclude_patterns']:
                if self.match_pattern(file_name, exclude_pattern):
                    excluded = True
                    break
            if excluded:
                continue
            
            # 检查匹配模式
            for pattern in config['patterns']:
                if self.match_pattern(file_name, pattern) or self.match_pattern(file_path, pattern):
                    return folder, config['name']
        
        return None, "未分类"
    
    def scan_files(self):
        """扫描项目目录中的所有文件"""
        all_files = []
        
        # 获取所有文件
        for root, dirs, files in os.walk(self.project_dir):
            # 跳过已经整理的目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in self.folder_structure.keys()]
            
            for file in files:
                if not file.startswith('.'):  # 跳过隐藏文件
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.project_dir)
                    
                    # 跳过已经在子目录中的文件
                    if os.path.dirname(rel_path) in self.folder_structure.keys():
                        continue
                    
                    file_size = os.path.getsize(file_path)
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    folder, category = self.categorize_file(rel_path)
                    
                    all_files.append({
                        'path': rel_path,
                        'full_path': file_path,
                        'name': file,
                        'size': file_size,
                        'mtime': file_mtime,
                        'folder': folder,
                        'category': category
                    })
        
        return sorted(all_files, key=lambda x: (x['folder'] or 'zzz', x['mtime']), reverse=True)
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def preview_organization(self):
        """预览文件整理计划"""
        files = self.scan_files()
        
        print("=" * 80)
        print("文件整理预览")
        print("=" * 80)
        print(f"扫描目录: {self.project_dir}")
        print(f"预览时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 按文件夹分组
        folder_groups = {}
        unorganized = []
        
        for file_info in files:
            if file_info['folder']:
                if file_info['folder'] not in folder_groups:
                    folder_groups[file_info['folder']] = []
                folder_groups[file_info['folder']].append(file_info)
            else:
                unorganized.append(file_info)
        
        # 显示整理计划
        total_files = 0
        for folder, config in self.folder_structure.items():
            if folder in folder_groups:
                files_in_folder = folder_groups[folder]
                total_size = sum(f['size'] for f in files_in_folder)
                total_files += len(files_in_folder)
                
                print(f"📁 {folder}/ ({config['name']}) - {len(files_in_folder)}个文件, {self.format_size(total_size)}")
                print(f"   说明: {config['description']}")
                
                for file_info in files_in_folder[:5]:  # 只显示前5个文件
                    print(f"   📄 {file_info['name']} ({self.format_size(file_info['size'])})")
                
                if len(files_in_folder) > 5:
                    print(f"   ... 还有 {len(files_in_folder) - 5} 个文件")
                print()
        
        if unorganized:
            print(f"❓ 未分类文件 - {len(unorganized)}个文件")
            for file_info in unorganized:
                print(f"   📄 {file_info['name']}")
            print()
        
        print(f"总计: {total_files}个文件将被整理")
        return total_files
    
    def organize_files(self, dry_run=False):
        """整理文件到对应文件夹"""
        files = self.scan_files()
        
        print(f"开始文件整理 ({'预览模式' if dry_run else '实际执行'})...")
        print("=" * 60)
        
        # 创建目录结构
        if not dry_run:
            for folder, config in self.folder_structure.items():
                folder_path = os.path.join(self.project_dir, folder)
                os.makedirs(folder_path, exist_ok=True)
                print(f"📁 创建目录: {folder}/ ({config['name']})")
        
        print()
        
        # 移动文件
        moved_files = 0
        errors = []
        
        for file_info in files:
            if file_info['folder']:
                source_path = file_info['full_path']
                target_dir = os.path.join(self.project_dir, file_info['folder'])
                target_path = os.path.join(target_dir, file_info['name'])
                
                try:
                    if not dry_run:
                        shutil.move(source_path, target_path)
                    
                    print(f"✅ {'[预览]' if dry_run else '已移动'} {file_info['name']} -> {file_info['folder']}/")
                    moved_files += 1
                    
                except Exception as e:
                    error_msg = f"移动失败 {file_info['name']}: {e}"
                    print(f"❌ {error_msg}")
                    errors.append(error_msg)
        
        print("\n" + "=" * 60)
        print(f"整理完成: {moved_files}个文件")
        
        if errors:
            print(f"错误: {len(errors)}个")
            for error in errors:
                print(f"  ❌ {error}")
        
        return moved_files, errors
    
    def create_folder_readme(self):
        """为每个文件夹创建README说明"""
        for folder, config in self.folder_structure.items():
            folder_path = os.path.join(self.project_dir, folder)
            if os.path.exists(folder_path):
                readme_path = os.path.join(folder_path, "README.md")
                
                readme_content = f"""# {config['name']}

## 说明
{config['description']}

## 文件类型
"""
                for pattern in config['patterns']:
                    readme_content += f"- `{pattern}`\n"
                
                if config['exclude_patterns']:
                    readme_content += f"\n## 排除文件\n"
                    for pattern in config['exclude_patterns']:
                        readme_content += f"- `{pattern}`\n"
                
                readme_content += f"\n## 更新时间\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                
                print(f"📄 创建说明文件: {folder}/README.md")

def main():
    """主函数"""
    organizer = FileOrganizer()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "preview":
            # 预览整理计划
            count = organizer.preview_organization()
            print(f"\n💡 提示:")
            print(f"  python3 organize_files.py organize  - 执行文件整理")
            
        elif command == "organize":
            # 执行文件整理
            confirm = input("确定要整理文件吗？这会移动文件到不同目录 (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                moved, errors = organizer.organize_files(dry_run=False)
                if moved > 0:
                    organizer.create_folder_readme()
                print(f"✅ 已整理 {moved} 个文件")
            else:
                print("❌ 已取消整理")
                
        elif command == "dry-run":
            # 预览模式执行
            moved, errors = organizer.organize_files(dry_run=True)
            print(f"计划整理 {moved} 个文件")
            
        else:
            print("❌ 未知命令")
            print("可用命令:")
            print("  preview   - 预览整理计划")
            print("  organize  - 执行文件整理")
            print("  dry-run   - 预览模式执行")
    else:
        # 默认预览
        organizer.preview_organization()

if __name__ == "__main__":
    main()
