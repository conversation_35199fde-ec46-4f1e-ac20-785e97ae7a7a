# ETF持仓查询脚本集合

## 功能描述
这是一套完整的ETF持仓数据获取和管理工具，包含单个ETF查询和批量ETF数据获取功能。

## 脚本文件说明
- **`top10.py`** - 单个ETF持仓查询脚本
- **`batch_etf_crawler.py`** - 批量ETF持仓数据获取脚本（支持CSV/XLSX/ODS和多工作表）
- **`format_converter.py`** - 表格文件格式转换工具
- **`file_manager.py`** - 项目文件管理和整理工具
- **`cleanup.py`** - 一键清理生成文件脚本
- **`update_ods_file.py`** - ODS文件更新工具
- **`demo.py`** - 演示和管理工具

## 功能特点
- 🔍 支持单个或批量ETF代码查询
- 📊 自动从多个数据源获取最新持仓信息
- 📋 **多格式支持**：CSV、Excel (XLSX)、OpenDocument (ODS)
- 📑 **多工作表支持**：可指定Excel/ODS文件中的特定工作表
- 💾 自动保存结果到带时间戳的文件
- 🛡️ 多重数据源备份，确保数据获取成功
- 📈 包含股票代码、名称、持仓比例等完整信息
- 🔄 支持从多种格式文件读取ETF列表并批量更新
- 🔀 内置格式转换工具，支持格式间相互转换
- 🗂️ 项目文件管理和一键清理功能
- ❌ 不使用模拟数据，确保信息真实性

## 安装依赖
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install requests beautifulsoup4 lxml pandas openpyxl odfpy
```

## 使用方法

### 1. 单个ETF查询（top10.py）

#### 方法1：命令行参数
```bash
python3 top10.py 562500    # 查询机器人ETF
python3 top10.py 510050    # 查询上证50ETF
python3 top10.py 159915    # 查询创业板ETF
```

#### 方法2：交互式输入
```bash
python3 top10.py
# 然后输入ETF代码，如：562500
```

### 2. 批量ETF查询（batch_etf_crawler.py）

#### 自动检测文件格式
```bash
python3 batch_etf_crawler.py
```
脚本会自动查找以下文件（按优先级）：
- `CoreETFTopTenHoldings.ods`
- `CoreETFTopTenHoldings.xlsx`
- `CoreETFTopTenHoldings.csv`

#### 指定输入文件
```bash
python3 batch_etf_crawler.py <文件路径> [工作表名称]
```
支持的文件格式：
- **CSV格式**: `python3 batch_etf_crawler.py data.csv`
- **Excel格式**: `python3 batch_etf_crawler.py data.xlsx`
- **ODS格式**: `python3 batch_etf_crawler.py data.ods`

#### 多工作表支持
```bash
# 指定Excel文件中的特定工作表
python3 batch_etf_crawler.py data.xlsx "大盘ETF"

# 指定ODS文件中的特定工作表
python3 batch_etf_crawler.py data.ods "中小盘ETF"
```

此脚本会：
- 自动识别并读取CSV/XLSX/ODS格式的ETF代码列表
- 批量获取所有ETF的最新持仓数据
- **同时生成三种格式的输出文件**：CSV、Excel、ODS
- 生成详细的文本汇总报告

### 3. 格式转换工具（format_converter.py）

#### 查看文件信息
```bash
python3 format_converter.py <文件> --info
```

#### 转换为指定格式
```bash
python3 format_converter.py <输入文件> <输出文件>
# 例如：
python3 format_converter.py data.csv data.xlsx
python3 format_converter.py data.xlsx data.ods
```

#### 转换为所有格式
```bash
python3 format_converter.py <输入文件> --all
```

### 4. 项目文件管理（file_manager.py）

#### 查看文件分类报告
```bash
python3 file_manager.py report
```

#### 保存文件报告
```bash
python3 file_manager.py save
```

#### 整理文件到目录
```bash
python3 file_manager.py preview    # 预览整理计划
python3 file_manager.py organize   # 执行文件整理
```

### 5. 一键清理工具（cleanup.py）

#### 查看清理报告
```bash
python3 cleanup.py report
```

#### 清理生成的文件
```bash
python3 cleanup.py preview    # 预览清理操作
python3 cleanup.py clean      # 执行清理
```

#### 清理旧文件
```bash
python3 cleanup.py preview-old 7    # 预览清理7天前的文件
python3 cleanup.py clean-old 7      # 清理7天前的文件
```

### 6. ODS文件更新（update_ods_file.py）

#### 更新ODS文件格式
```bash
python3 update_ods_file.py
```
此脚本会：
- 读取Excel格式的数据文件
- 转换并保存为ODS格式
- 创建原文件备份

## 输出文件

### 单个ETF查询输出
- `etf_XXXXXX_top10_YYYYMMDD_HHMMSS.txt` - 单个ETF的持仓数据文件

### 批量查询输出
- `all_etf_holdings_YYYYMMDD_HHMMSS.txt` - 所有ETF的文本汇总报告
- `<输入文件名>_updated.csv` - CSV格式的数据文件
- `<输入文件名>_updated.xlsx` - Excel格式的数据文件
- `<输入文件名>_updated.ods` - ODS格式的数据文件
- `<原文件名>_backup_YYYYMMDD_HHMMSS.*` - 原文件备份（如果需要）

## 输出内容示例

### 控制台输出
```
================================================================================
ETF（562500）- 华夏中证机器人ETF 最新季度持仓前十大股票
================================================================================
排名   股票代码       股票名称                 持仓比例
--------------------------------------------------------------------------------
1    300124     汇川技术                 9.93%
2    002230     科大讯飞                 9.66%
3    688169     石头科技                 5.01%
...
```

### 文件输出（表格格式）
```
┌────────────┬────────────────────┬────────────────────────────────────────────────────────────────────────────────┐
│    代码    │        名称        │                                     十大持仓                                     │
├────────────┼────────────────────┼────────────────────────────────────────────────────────────────────────────────┤
│  562500  │    华夏中证机器人ETF    │汇川技术(9.93%)、科大讯飞(9.66%)、石头科技(5.01%)、大华股                                       │
│            │                    │份(4.87%)、中控技术(4.40%)、双环传动(3.51%)、机器人(3.33%)、                                  │
│            │                    │大族激光(3.17%)、巨轮智能(2.76%)、拓邦股份(2.66%)                                           │
└────────────┴────────────────────┴────────────────────────────────────────────────────────────────────────────────┘

获取时间: 2025-09-15 14:45:39
注：数据来源于公开信息，仅供参考
```

## 数据源
脚本会尝试从以下数据源获取信息：
1. 东方财富网
2. 天天基金网
3. 新浪财经

如果网络数据源无法访问，会使用基于最新公开信息的备用数据。

## 支持的ETF示例
- **562500** - 华夏中证机器人ETF
- **510050** - 华夏上证50ETF
- **159915** - 易方达创业板ETF
- **510300** - 华泰柏瑞沪深300ETF
- **159919** - 嘉实沪深300ETF
- **512100** - 南方中证500ETF

## 注意事项
- ✅ 不使用任何模拟数据，确保信息真实性
- 📊 数据仅供参考，投资需谨慎
- 🔄 持仓信息通常按季度更新
- 🌐 网络连接可能影响数据获取速度
- 📋 输出格式为表格样式，便于阅读和复制

## 文件结构
- `top10.py` - 单个ETF查询脚本
- `batch_etf_crawler.py` - 批量ETF查询脚本
- `update_ods_file.py` - ODS文件更新工具
- `requirements.txt` - 依赖包列表
- `README.md` - 使用说明
- `CoreETFTopTenHoldings.ods` - ETF代码列表文件（输入）
- `etf_*_top10_*.txt` - 单个ETF持仓数据文件（输出）
- `all_etf_holdings_*.txt` - 批量查询汇总报告（输出）
- `CoreETFTopTenHoldings_updated.*` - 更新后的数据文件（输出）

## 工作流程

### 完整的批量更新流程
1. **准备ETF列表**：确保 `CoreETFTopTenHoldings.ods` 文件包含要查询的ETF代码
2. **批量获取数据**：运行 `python3 batch_etf_crawler.py`
3. **查看结果**：
   - 文本报告：`all_etf_holdings_*.txt`
   - Excel文件：`CoreETFTopTenHoldings_updated.xlsx`
   - ODS文件：`CoreETFTopTenHoldings_updated_new.ods`

### 单个ETF查询流程
1. **运行脚本**：`python3 top10.py [ETF代码]`
2. **查看结果**：`etf_*_top10_*.txt`
