#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF持仓分析工具 - 一键版本
支持单个ETF查询和批量Excel文件处理
"""

import requests
from datetime import datetime
import re
from bs4 import BeautifulSoup
import sys
import os
import time
import pandas as pd
import traceback

class ETFAnalyzer:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def is_valid_stock_code(self, stock_code):
        """验证股票代码是否有效，支持全球多种格式"""
        if not stock_code or len(stock_code) < 2:
            return False
        
        # A股代码：6位数字
        if len(stock_code) == 6 and stock_code.isdigit():
            return True
        # 港股代码：5位，以0开头
        if len(stock_code) == 5 and stock_code.startswith('0') and stock_code[1:].isdigit():
            return True
        # 美股代码：1-5位字母
        if 1 <= len(stock_code) <= 5 and stock_code.isalpha():
            return True
        # 欧洲股票代码：字母+数字组合
        if 2 <= len(stock_code) <= 15 and re.match(r'^[A-Za-z0-9]+$', stock_code):
            return True
        
        return False

    def is_bond_etf(self, etf_code):
        """判断是否为债券ETF"""
        # 债券ETF通常以511开头，或者包含特定关键词
        bond_prefixes = ['511', '551']  # 债券ETF常见前缀
        return any(etf_code.startswith(prefix) for prefix in bond_prefixes)

    def get_bond_etf_holdings(self, etf_code):
        """获取债券ETF持仓数据"""
        try:
            # 尝试多个数据源URL获取债券持仓
            urls = [
                f"https://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=zqcc&code={etf_code}&topline=10",
                f"http://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=zqcc&code={etf_code}&topline=10",
                f"https://fund.eastmoney.com/{etf_code}.html",
                f"http://fundf10.eastmoney.com/zqcc_{etf_code}.html"
            ]

            for url in urls:
                try:
                    response = requests.get(url, headers=self.headers, timeout=15)
                    response.encoding = 'utf-8'

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        holdings = self.parse_bond_holdings_html(soup)
                        if holdings:
                            return holdings
                except:
                    continue

            # 如果无法获取具体债券持仓，返回通用标识
            etf_name = self.get_etf_name(etf_code)
            return [{'rank': 1, 'stock_code': etf_code, 'stock_name': f'{etf_name}(债券组合)', 'holding_ratio': '债券ETF'}]
        except Exception as e:
            print(f"获取债券ETF数据失败: {e}")
            return None

    def parse_bond_holdings_html(self, soup):
        """解析HTML获取债券持仓数据"""
        try:
            holdings = []
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')
                if len(rows) > 1:
                    header_row = rows[0]
                    header_cells = header_row.find_all(['th', 'td'])
                    header_text = ' '.join([cell.get_text().strip() for cell in header_cells])

                    # 查找债券持仓相关的表格
                    if any(keyword in header_text for keyword in ['债券名称', '债券代码', '持仓占比', '占净值比例', '债券持仓']):
                        # 找到列位置
                        code_col = name_col = ratio_col = -1
                        for i, cell in enumerate(header_cells):
                            cell_text = cell.get_text().strip()
                            if '债券代码' in cell_text or '代码' in cell_text:
                                code_col = i
                            elif '债券名称' in cell_text or '名称' in cell_text:
                                name_col = i
                            elif '持仓占比' in cell_text or '占净值比例' in cell_text or '比例' in cell_text:
                                ratio_col = i

                        # 解析数据行
                        for i, row in enumerate(rows[1:11]):  # 前10行
                            cells = row.find_all('td')
                            if len(cells) >= 2:
                                bond_code = ""
                                bond_name = ""
                                holding_ratio = ""

                                # 按列位置提取数据
                                if code_col >= 0 and code_col < len(cells):
                                    bond_code = cells[code_col].get_text().strip()
                                if name_col >= 0 and name_col < len(cells):
                                    bond_name = cells[name_col].get_text().strip()
                                if ratio_col >= 0 and ratio_col < len(cells):
                                    holding_ratio = cells[ratio_col].get_text().strip()

                                # 如果没找到明确列位置，按常见格式解析
                                if not bond_name and len(cells) >= 2:
                                    # 尝试不同的列组合
                                    for j in range(len(cells)):
                                        cell_text = cells[j].get_text().strip()
                                        if cell_text and not cell_text.isdigit() and '%' not in cell_text:
                                            bond_name = cell_text
                                            break

                                    # 查找比例
                                    for j in range(len(cells)):
                                        cell_text = cells[j].get_text().strip()
                                        if '%' in cell_text:
                                            holding_ratio = cell_text
                                            break

                                # 清理数据
                                if holding_ratio:
                                    ratio_match = re.search(r'(\d+\.?\d*%)', holding_ratio)
                                    if ratio_match:
                                        holding_ratio = ratio_match.group(1)

                                # 验证并添加数据
                                if bond_name and bond_name not in ['合计', '总计', '小计']:
                                    holdings.append({
                                        'rank': i + 1,
                                        'stock_code': bond_code if bond_code else f'BOND{i+1:02d}',
                                        'stock_name': bond_name,
                                        'holding_ratio': holding_ratio if holding_ratio else '--'
                                    })

                        if holdings:
                            return holdings

            return None

        except Exception as e:
            print(f"解析债券数据失败: {e}")
            return None

    def get_etf_holdings(self, etf_code):
        """获取ETF持仓数据"""
        try:
            # 检查是否为债券ETF
            if self.is_bond_etf(etf_code):
                return self.get_bond_etf_holdings(etf_code)

            # 尝试多个数据源URL
            urls = [
                f"https://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=jjcc&code={etf_code}&topline=10",
                f"http://fund.eastmoney.com/f10/FundArchivesDatas.aspx?type=jjcc&code={etf_code}&topline=10",
                f"http://fundf10.eastmoney.com/ccmx_{etf_code}.html"
            ]

            for url in urls:
                try:
                    response = requests.get(url, headers=self.headers, timeout=15)
                    response.encoding = 'utf-8'

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        holdings = self.parse_holdings_html(soup)
                        if holdings:
                            return holdings
                except:
                    continue

            return None

        except Exception as e:
            print(f"获取数据失败: {e}")
            return None

    def parse_holdings_html(self, soup):
        """解析HTML获取持仓数据"""
        try:
            holdings = []
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')
                if len(rows) > 1:
                    header_row = rows[0]
                    header_cells = header_row.find_all(['th', 'td'])
                    header_text = ' '.join([cell.get_text().strip() for cell in header_cells])

                    if any(keyword in header_text for keyword in ['股票代码', '股票名称', '持仓比例', '占净值比例']):
                        # 找到列位置
                        code_col = name_col = ratio_col = -1
                        for i, cell in enumerate(header_cells):
                            cell_text = cell.get_text().strip()
                            if '股票代码' in cell_text or '证券代码' in cell_text:
                                code_col = i
                            elif '股票名称' in cell_text or '证券名称' in cell_text:
                                name_col = i
                            elif '持仓比例' in cell_text or '占净值比例' in cell_text:
                                ratio_col = i

                        # 解析数据行
                        for i, row in enumerate(rows[1:11]):  # 前10行
                            cells = row.find_all('td')
                            if len(cells) >= 3:
                                stock_code = ""
                                stock_name = ""
                                holding_ratio = ""
                                
                                # 按列位置提取数据
                                if code_col >= 0 and code_col < len(cells):
                                    stock_code = cells[code_col].get_text().strip()
                                if name_col >= 0 and name_col < len(cells):
                                    stock_name = cells[name_col].get_text().strip()
                                if ratio_col >= 0 and ratio_col < len(cells):
                                    holding_ratio = cells[ratio_col].get_text().strip()

                                # 如果没找到明确列位置，按常见格式解析
                                if not stock_code and not stock_name and len(cells) >= 3:
                                    stock_code = cells[1].get_text().strip()
                                    stock_name = cells[2].get_text().strip()
                                    for j in range(3, min(len(cells), 8)):
                                        cell_text = cells[j].get_text().strip()
                                        if '%' in cell_text:
                                            holding_ratio = cell_text
                                            break

                                # 清理数据
                                if stock_code:
                                    stock_code = re.sub(r'[^\d\w]', '', stock_code)
                                if holding_ratio:
                                    ratio_match = re.search(r'(\d+\.?\d*%)', holding_ratio)
                                    if ratio_match:
                                        holding_ratio = ratio_match.group(1)

                                # 验证并添加数据
                                if stock_code and stock_name and self.is_valid_stock_code(stock_code):
                                    holdings.append({
                                        'rank': i + 1,
                                        'stock_code': stock_code,
                                        'stock_name': stock_name,
                                        'holding_ratio': holding_ratio
                                    })

                        if holdings:
                            return holdings

            return None

        except Exception as e:
            print(f"解析数据失败: {e}")
            return None

    def get_etf_name(self, etf_code):
        """获取ETF名称"""
        try:
            url = f"https://fund.eastmoney.com/{etf_code}.html"
            response = requests.get(url, headers=self.headers, timeout=10)
            response.encoding = 'utf-8'

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                title_elements = soup.find_all(['title', 'h1', 'h2'])
                for element in title_elements:
                    text = element.get_text().strip()
                    if etf_code in text and ('ETF' in text or '基金' in text):
                        name_match = re.search(r'([^(（]+(?:ETF|基金))', text)
                        if name_match:
                            return name_match.group(1).strip()
        except:
            pass

        return f"ETF({etf_code})"

    def format_holdings_text(self, holdings):
        """格式化持仓数据为文本"""
        if not holdings:
            return "暂无数据"
        
        result = []
        for holding in holdings:
            name = holding.get('stock_name', '').strip()
            ratio = holding.get('holding_ratio', '').strip()
            if name:
                if ratio:
                    result.append(f"{name}({ratio})")
                else:
                    result.append(name)
        
        return "、".join(result)

    def query_single_etf(self, etf_code):
        """查询单个ETF"""
        print(f"正在查询ETF {etf_code}...")
        
        # 获取ETF名称和持仓
        etf_name = self.get_etf_name(etf_code)
        holdings = self.get_etf_holdings(etf_code)
        
        if holdings:
            print(f"\n✅ {etf_name} 前十大持仓:")
            print("-" * 80)
            for holding in holdings:
                print(f"{holding['rank']:2d}. {holding['stock_code']:<8} {holding['stock_name']:<25} {holding['holding_ratio']}")
            print("-" * 80)
            
            # 保存到文件
            filename = f"etf_{etf_code}_holdings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            holdings_text = self.format_holdings_text(holdings)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"ETF代码: {etf_code}\n")
                f.write(f"ETF名称: {etf_name}\n")
                f.write(f"十大持仓: {holdings_text}\n")
                f.write(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            print(f"✅ 数据已保存到: {filename}")
            return True
        else:
            print(f"❌ 无法获取ETF {etf_code} 的持仓数据")
            return False

    def process_excel_file(self, excel_file, sheet_names=None):
        """处理Excel文件中的ETF列表"""
        try:
            print(f"正在处理Excel文件: {excel_file}")
            
            # 读取Excel文件
            if sheet_names:
                print(f"指定工作表: {', '.join(sheet_names)}")
                all_sheets = pd.read_excel(excel_file, sheet_name=sheet_names, engine='openpyxl')
            else:
                all_sheets = pd.read_excel(excel_file, sheet_name=None, engine='openpyxl')
                print(f"发现工作表: {', '.join(all_sheets.keys())}")

            # 如果只有一个工作表，转换为字典格式
            if isinstance(all_sheets, pd.DataFrame):
                all_sheets = {'Sheet1': all_sheets}

            total_processed = 0
            total_success = 0
            results = {}

            # 处理每个工作表
            for sheet_name, df in all_sheets.items():
                print(f"\n📋 处理工作表: {sheet_name}")
                
                # 提取ETF代码
                etf_codes = []
                for _, row in df.iterrows():
                    for col in df.columns:
                        value = str(row[col]).strip()
                        if re.match(r'^\d{6}$', value):  # 6位数字ETF代码
                            if value not in etf_codes:
                                etf_codes.append(value)
                            break

                print(f"找到 {len(etf_codes)} 个ETF代码")
                
                # 处理每个ETF
                sheet_results = {}
                for i, etf_code in enumerate(etf_codes, 1):
                    print(f"[{i}/{len(etf_codes)}] 处理 {etf_code}...")
                    
                    etf_name = self.get_etf_name(etf_code)
                    holdings = self.get_etf_holdings(etf_code)
                    
                    if holdings:
                        holdings_text = self.format_holdings_text(holdings)
                        sheet_results[etf_code] = {
                            'name': etf_name,
                            'holdings': holdings_text,
                            'status': 'success'
                        }
                        total_success += 1
                        print(f"  ✅ 成功")
                    else:
                        sheet_results[etf_code] = {
                            'name': etf_name,
                            'holdings': '获取失败',
                            'status': 'failed'
                        }
                        print(f"  ❌ 失败")
                    
                    total_processed += 1
                    
                    # 避免请求过快
                    if i < len(etf_codes):
                        time.sleep(2)

                results[sheet_name] = sheet_results

            # 生成结果文件
            self.save_results(excel_file, all_sheets, results)
            
            print(f"\n🎉 处理完成!")
            print(f"总计: {total_processed} 个ETF")
            print(f"成功: {total_success} 个")
            print(f"失败: {total_processed - total_success} 个")
            print(f"成功率: {total_success/total_processed*100:.1f}%")
            
            return True

        except Exception as e:
            print(f"❌ 处理Excel文件失败: {e}")
            traceback.print_exc()
            return False

    def save_results(self, original_file, all_sheets, results):
        """保存处理结果"""
        try:
            base_name = os.path.splitext(original_file)[0]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 更新原Excel文件
            output_excel = f"{base_name}_updated.xlsx"
            
            with pd.ExcelWriter(output_excel, engine='openpyxl') as writer:
                for sheet_name, df in all_sheets.items():
                    # 复制原数据
                    updated_df = df.copy()
                    
                    # 添加十大持仓列（如果不存在）
                    if '十大持仓' not in updated_df.columns:
                        updated_df['十大持仓'] = ''
                    
                    # 更新持仓数据
                    if sheet_name in results:
                        sheet_results = results[sheet_name]
                        for idx, row in updated_df.iterrows():
                            for col in updated_df.columns:
                                value = str(row[col]).strip()
                                if re.match(r'^\d{6}$', value) and value in sheet_results:
                                    # 确保'十大持仓'列存在且为object类型
                                    if '十大持仓' not in updated_df.columns:
                                        updated_df['十大持仓'] = ''
                                    updated_df['十大持仓'] = updated_df['十大持仓'].astype('object')
                                    updated_df.loc[idx, '十大持仓'] = sheet_results[value]['holdings']
                                    break
                    
                    # 写入工作表
                    updated_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print(f"✅ Excel文件已更新: {output_excel}")
            
            # 生成CSV汇总文件
            csv_data = []
            for sheet_name, sheet_results in results.items():
                for etf_code, data in sheet_results.items():
                    csv_data.append({
                        '工作表': sheet_name,
                        'ETF代码': etf_code,
                        'ETF名称': data['name'],
                        '十大持仓': data['holdings'],
                        '状态': data['status'],
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
            
            csv_file = f"{base_name}_summary_{timestamp}.csv"
            pd.DataFrame(csv_data).to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"✅ CSV汇总文件已生成: {csv_file}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    analyzer = ETFAnalyzer()
    
    print("🎯 ETF持仓分析工具")
    print("=" * 50)
    
    if len(sys.argv) == 1:
        # 交互模式
        print("请选择功能:")
        print("1. 查询单个ETF")
        print("2. 处理Excel文件")
        choice = input("请输入选择 (1/2): ").strip()
        
        if choice == '1':
            etf_code = input("请输入ETF代码: ").strip()
            if etf_code:
                analyzer.query_single_etf(etf_code)
        elif choice == '2':
            excel_file = input("请输入Excel文件路径: ").strip()
            if excel_file and os.path.exists(excel_file):
                analyzer.process_excel_file(excel_file)
            else:
                print("❌ 文件不存在")
        else:
            print("❌ 无效选择")
    
    elif len(sys.argv) == 2:
        arg = sys.argv[1]
        if re.match(r'^\d{6}$', arg):
            # 单个ETF代码
            analyzer.query_single_etf(arg)
        elif os.path.exists(arg):
            # Excel文件
            analyzer.process_excel_file(arg)
        else:
            print(f"❌ 无效参数: {arg}")
    
    elif len(sys.argv) >= 3:
        excel_file = sys.argv[1]
        sheet_names = sys.argv[2:]
        if os.path.exists(excel_file):
            analyzer.process_excel_file(excel_file, sheet_names)
        else:
            print(f"❌ 文件不存在: {excel_file}")


if __name__ == "__main__":
    main()
