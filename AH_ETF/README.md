# 📊 ETF持仓分析工具

一键获取ETF前十大持仓数据，支持单个查询和批量Excel处理。

## 🚀 快速开始

### 1. 一键安装
```bash
python3 install.py
```

### 2. 开始使用
```bash
# 交互式使用（推荐新手）
python3 etf_analyzer.py

# 查询单个ETF
python3 etf_analyzer.py 513330

# 处理Excel文件
python3 etf_analyzer.py input/核心ETF十大持仓.xlsx
```

## ✨ 主要功能

- 🔍 **单个ETF查询** - 输入代码即可获取持仓
- 📊 **批量Excel处理** - 一次处理多个ETF
- 🌍 **全球市场支持** - A股、港股、美股、欧股
- 📁 **多工作表支持** - 处理复杂Excel文件
- 💾 **多格式输出** - Excel、CSV格式

## 📁 文件结构

```
ETF/
├── etf_analyzer.py    # 主程序
├── install.py         # 一键安装脚本
├── README.md          # 本文件
├── 使用说明.md        # 详细使用说明
├── input/             # 输入文件夹
└── output/            # 输出文件夹
```

## 🎯 使用示例

### 查询华夏恒生互联网科技业ETF
```bash
$ python3 etf_analyzer.py 513330

✅ 华夏恒生互联网科技业ETF 前十大持仓:
 1. 01024    快手-W           12.48%
 2. 09999    网易-S           11.48%
 3. 00700    腾讯控股         10.87%
 4. 09618    京东集团-SW      10.66%
 5. 09988    阿里巴巴-W       10.28%
 ...
```

### 批量处理Excel文件
```bash
$ python3 etf_analyzer.py input/核心ETF十大持仓.xlsx

🎉 处理完成!
总计: 148 个ETF
成功: 125 个 (84.5%)
✅ 结果已保存到更新的Excel文件
```

## 🌍 支持的ETF类型

- 🇨🇳 **A股ETF** - 沪深300、中证500、创业板等
- 🇭🇰 **港股ETF** - 恒生指数、恒生科技等
- 🇺🇸 **美股ETF** - 纳斯达克100、标普500等
- 🇪🇺 **欧股ETF** - 法国CAC40、德国DAX等

## 📋 环境要求

- Python 3.6+
- 网络连接

依赖包会自动安装：
- requests
- beautifulsoup4
- pandas
- openpyxl

## 📖 详细说明

查看 `使用说明.md` 获取完整的使用指南和示例。

---

**🎉 立即开始：`python3 install.py` 然后 `python3 etf_analyzer.py`**
