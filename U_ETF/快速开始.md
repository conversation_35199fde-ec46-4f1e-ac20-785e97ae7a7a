# 🚀 美股ETF持仓分析工具 - 快速开始

## 📋 三步开始使用

### 第一步：安装依赖
```bash
python3 install.py
```

### 第二步：查询单个ETF
```bash
python3 etf_analyzer.py VOO
```

### 第三步：批量处理CSV文件
```bash
python3 etf_analyzer.py input/核心ETF十大持仓.csv
```

## 🎯 常用命令

### 查询热门ETF
```bash
# Vanguard S&P 500 ETF
python3 etf_analyzer.py VOO

# SPDR S&P 500 ETF
python3 etf_analyzer.py SPY

# Invesco QQQ Trust
python3 etf_analyzer.py QQQ

# Vanguard Total Stock Market ETF
python3 etf_analyzer.py VTI

# Vanguard FTSE Developed Markets ETF
python3 etf_analyzer.py VEA
```

### 指定数据源
```bash
# 使用Yahoo Finance
python3 etf_analyzer.py VOO --source 3

# 使用Vanguard官网
python3 etf_analyzer.py VOO --source 2

# 使用yfinance
python3 etf_analyzer.py VOO --source 1
```

### 调试模式
```bash
python3 etf_analyzer.py VOO --debug
```

## 📊 输出示例

```
✅ Vanguard S&P 500 ETF 前十大持仓:
--------------------------------------------------
 1. NVDA     8.07%
 2. MSFT     7.38%
 3. AAPL     5.77%
 4. AMZN     4.12%
 5. META     3.12%
 6. AVGO     2.57%
 7. GOOGL    2.08%
 8. GOOG     1.68%
 9. BRK-B    1.61%
10. TSLA     1.61%
--------------------------------------------------
```

## 📁 文件位置

- **输入文件**: `input/` 文件夹
- **输出文件**: `output/` 文件夹
- **详细说明**: `使用说明.md`

## ❓ 遇到问题？

1. **网络问题**: 确保网络连接正常，建议使用代理
2. **依赖问题**: 运行 `python3 install.py` 重新安装
3. **ETF代码**: 确保使用正确的美股ETF代码

---

**🎉 就这么简单！开始探索美股ETF的世界吧！**
