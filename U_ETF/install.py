#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美股ETF持仓分析工具 - 一键安装脚本
"""

import subprocess
import sys
import os

def install_requirements():
    """安装必要的Python包"""
    required_packages = [
        'requests',
        'beautifulsoup4',
        'pandas',
        'openpyxl',
        'yfinance',
        'selenium',
        'webdriver-manager'
    ]
    
    print("🔧 正在安装必要的Python包...")
    
    for package in required_packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = ['input', 'output']
    
    print("\n📁 创建目录结构...")
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"📁 目录已存在: {directory}")

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        import requests
        import bs4
        import pandas
        import openpyxl
        import yfinance
        import selenium
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ 所有依赖包导入成功")
        
        # 测试主程序
        if os.path.exists('etf_analyzer.py'):
            print("✅ 主程序文件存在")
        else:
            print("❌ 主程序文件不存在")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 美股ETF持仓分析工具 - 一键安装")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖包
    if not install_requirements():
        print("\n❌ 安装失败，请检查网络连接或手动安装")
        return
    
    # 创建目录
    create_directories()
    
    # 测试安装
    if test_installation():
        print("\n🎉 安装完成！")
        print("\n📖 使用方法:")
        print("1. 交互式使用: python3 etf_analyzer.py")
        print("2. 查询单个美股ETF: python3 etf_analyzer.py VOO")
        print("3. 处理CSV文件: python3 etf_analyzer.py input/核心ETF十大持仓.csv")
        print("\n📋 详细说明请查看: 使用说明.md")
        print("\n🌟 支持的美股ETF示例:")
        print("   VOO - Vanguard S&P 500 ETF")
        print("   SPY - SPDR S&P 500 ETF")
        print("   QQQ - Invesco QQQ Trust")
        print("   VTI - Vanguard Total Stock Market ETF")
        print("   VEA - Vanguard FTSE Developed Markets ETF")
    else:
        print("\n❌ 安装测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
