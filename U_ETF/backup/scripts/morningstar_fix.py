#!/usr/bin/env python3
"""
Morningstar数据获取修复脚本
处理202状态码和异步加载问题
"""

import requests
import time
from bs4 import BeautifulSoup
import re
import json

class MorningstarFixer:
    def __init__(self, proxy=None):
        self.proxy = proxy
        self.session = requests.Session()
        if proxy:
            self.session.proxies = {'http': proxy, 'https': proxy}
        
        # 更真实的浏览器请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
    
    def get_morningstar_holdings(self, symbol):
        """获取Morningstar持仓数据"""
        print(f"\n🔍 修复版Morningstar测试 - {symbol}")
        
        # 尝试多种URL格式
        url_patterns = [
            f"https://www.morningstar.com/etfs/xnas/{symbol.lower()}/portfolio",
            f"https://www.morningstar.com/etfs/arcx/{symbol.lower()}/portfolio", 
            f"https://www.morningstar.com/etfs/{symbol.lower()}/portfolio",
            f"https://www.morningstar.com/etfs/xnas/{symbol.lower()}",
            f"https://www.morningstar.com/etfs/arcx/{symbol.lower()}",
            f"https://www.morningstar.com/etfs/{symbol.lower()}",
            f"https://www.morningstar.com/funds/xnas/{symbol.lower()}/portfolio",
            f"https://www.morningstar.com/funds/{symbol.lower()}/portfolio"
        ]
        
        for url in url_patterns:
            try:
                print(f"  尝试URL: {url}")
                
                # 第一次请求
                response = self.session.get(url, timeout=20)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 202:
                    print("  收到202状态码，等待5秒后重试...")
                    time.sleep(5)
                    
                    # 重试请求
                    response = self.session.get(url, timeout=20)
                    print(f"  重试状态码: {response.status_code}")
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 保存调试文件
                    with open(f'debug_morningstar_{symbol}.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"  调试文件已保存: debug_morningstar_{symbol}.html")
                    
                    # 解析持仓数据
                    holdings = self.parse_morningstar_content(soup, symbol)
                    if holdings:
                        print(f"  ✅ 成功解析 {len(holdings)} 个持仓:")
                        for h in holdings:
                            print(f"    {h}")
                        return holdings
                    else:
                        print("  ❌ 未能解析出持仓数据")
                        
                        # 检查页面内容
                        page_text = soup.get_text()
                        if any(keyword in page_text.lower() for keyword in ['holdings', 'portfolio', 'top 10']):
                            print("  ℹ️ 页面包含持仓相关内容，但解析失败")
                        else:
                            print("  ℹ️ 页面不包含持仓相关内容")
                
                elif response.status_code == 404:
                    print("  ❌ 404 Not Found")
                    continue
                elif response.status_code == 403:
                    print("  ❌ 403 Forbidden")
                    continue
                else:
                    print(f"  ❌ 未处理的状态码: {response.status_code}")
                    continue
                    
            except Exception as e:
                print(f"  异常: {e}")
                continue
        
        return None
    
    def parse_morningstar_content(self, soup, symbol):
        """解析Morningstar页面内容"""
        holdings = []
        
        try:
            # 方法1: 查找JSON数据
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string and 'portfolio' in script.string.lower():
                    try:
                        # 尝试提取JSON数据
                        json_match = re.search(r'(\{.*"portfolio".*\})', script.string)
                        if json_match:
                            data = json.loads(json_match.group(1))
                            print("  找到JSON数据")
                            # 这里可以进一步解析JSON
                    except:
                        pass
            
            # 方法2: 查找表格数据
            tables = soup.find_all('table')
            print(f"  找到 {len(tables)} 个表格")
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows[1:11]:  # 跳过表头
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 3:
                        # 尝试提取股票代码、名称和权重
                        cell_texts = [cell.get_text().strip() for cell in cells]
                        
                        # 查找包含股票代码的单元格
                        for i, text in enumerate(cell_texts):
                            if re.match(r'^[A-Z]{2,6}$', text):
                                symbol_text = text
                                name_text = cell_texts[i+1] if i+1 < len(cell_texts) else ""
                                weight_text = ""
                                
                                # 查找权重
                                for j, cell_text in enumerate(cell_texts):
                                    if '%' in cell_text and re.search(r'\d+\.\d+%', cell_text):
                                        weight_text = cell_text
                                        break
                                
                                if symbol_text and name_text and weight_text:
                                    holdings.append(f"{symbol_text} - {name_text} ({weight_text})")
                                    if len(holdings) >= 10:
                                        break
                
                if holdings:
                    break
            
            # 方法3: 文本模式匹配
            if not holdings:
                page_text = soup.get_text()
                known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'GOOG', 'AVGO', 'TSLA', 'BRK-B']
                
                # 查找股票代码模式
                pattern = r'([A-Z]{2,6})\s+([^0-9\n]+?)\s+(\d+\.\d+%)'
                matches = re.findall(pattern, page_text)
                
                for match in matches:
                    symbol_part, name_part, weight_part = match
                    if symbol_part in known_stocks and len(holdings) < 10:
                        holdings.append(f"{symbol_part} - {name_part.strip()} ({weight_part})")
            
        except Exception as e:
            print(f"  解析异常: {e}")
        
        return holdings[:10] if holdings else None

def main():
    # 自动检测代理
    proxy = None
    try:
        test_response = requests.get('http://127.0.0.1:8001', timeout=2)
        proxy = 'http://127.0.0.1:8001'
        print(f"🌐 检测到代理: {proxy}")
    except:
        print("🌐 未检测到代理，直接连接")
    
    fixer = MorningstarFixer(proxy=proxy)
    
    # 测试多个ETF
    test_symbols = ['VOO', 'SPY', 'QQQ', 'ARKK', 'VTI']
    
    for symbol in test_symbols:
        holdings = fixer.get_morningstar_holdings(symbol)
        if holdings:
            print(f"\n✅ {symbol} Morningstar持仓数据获取成功!")
        else:
            print(f"\n❌ {symbol} Morningstar持仓数据获取失败")
        
        time.sleep(3)  # 避免请求过快

if __name__ == "__main__":
    main()
