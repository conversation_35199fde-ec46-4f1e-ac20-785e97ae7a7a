# 📊 美股ETF持仓分析工具

一键获取美股ETF前十大持仓数据，支持单个查询和批量CSV处理。

## 🚀 快速开始

### 1. 一键安装
```bash
python3 install.py
```

### 2. 开始使用
```bash
# 交互式使用（推荐新手）
python3 etf_analyzer.py

# 查询单个ETF
python3 etf_analyzer.py VOO

# 处理CSV文件
python3 etf_analyzer.py input/核心ETF十大持仓.csv
```

## ✨ 主要功能

- 🔍 **单个ETF查询** - 输入代码即可获取持仓
- 📊 **批量CSV处理** - 一次处理多个ETF
- 🌍 **多数据源支持** - Yahoo Finance、Vanguard、yfinance
- 🌐 **国际股票支持** - 美股、欧股、亚股等全球市场
- 💾 **多格式输出** - TXT、CSV格式

## 📁 文件结构

```
U_ETF/
├── etf_analyzer.py    # 主程序
├── run.py            # 用户友好启动器
├── install.py        # 一键安装脚本
├── README.md         # 本文件
├── 使用说明.md       # 详细使用说明
├── input/            # 输入文件夹
├── output/           # 输出文件夹
└── backup/           # 备份文件夹
```

## 🎯 使用示例

### 查询Vanguard S&P 500 ETF
```bash
$ python3 etf_analyzer.py VOO

✅ Vanguard S&P 500 Index Fund ETF 前十大持仓:
 1. NVDA     8.07%
 2. MSFT     7.38%
 3. AAPL     5.77%
 4. AMZN     4.12%
 5. META     3.12%
 ...
```

### 批量处理CSV文件
```bash
$ python3 etf_analyzer.py input/核心ETF十大持仓.csv

🎉 处理完成!
总计: 8 个美股ETF
成功: 8 个 (100.0%)
✅ 结果已保存到更新的CSV文件
```

## 🌍 支持的ETF类型

- 🇺🇸 **美股ETF** - SPY、VOO、QQQ、VTI等
- 🌍 **国际ETF** - VEA、IEFA、VWO等
- 🏢 **行业ETF** - XLK、XLF、XLE等
- 💰 **主题ETF** - ARKK、ICLN、BOTZ等

## 📋 环境要求

- Python 3.6+
- 网络连接（支持代理）

依赖包会自动安装：
- requests
- beautifulsoup4
- pandas
- selenium
- webdriver-manager

## 📖 详细说明

查看 `使用说明.md` 获取完整的使用指南和示例。

---

**🎉 立即开始：`python3 install.py` 然后 `python3 etf_analyzer.py`**
