#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美股ETF持仓分析工具 - 简化启动器
适合小白用户使用的友好界面
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'requests', 'beautifulsoup4', 'pandas', 'openpyxl',
        'yfinance', 'selenium', 'webdriver-manager'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """安装缺失的依赖"""
    print("🔧 正在安装必要的依赖包...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install',
                             'requests', 'beautifulsoup4', 'pandas', 'openpyxl',
                             'yfinance', 'selenium', 'webdriver-manager'])
        print("✅ 依赖安装完成！")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败，请手动安装")
        return False

def show_welcome():
    """显示欢迎界面"""
    print("=" * 60)
    print("🎯 美股ETF持仓分析工具")
    print("=" * 60)
    print("📊 支持获取美股ETF前十大持仓数据")
    print("🌐 多数据源支持：Yahoo Finance、Vanguard等")
    print("📋 支持批量处理Excel文件")
    print("=" * 60)

def show_main_menu():
    """显示主菜单"""
    print("\n📋 请选择操作模式：")
    print("1. 🔍 查询单个ETF")
    print("2. 📊 处理Excel/CSV文件")
    print("3. ⚙️  数据源设置")
    print("4. 📖 使用帮助")
    print("5. 🚪 退出程序")
    print("-" * 40)

def show_data_source_menu():
    """显示数据源选择菜单"""
    print("\n📊 可用数据源:")
    print("1. yfinance库 (快速，基础验证)")
    print("2. Vanguard Selenium (官方数据，最准确，较慢) ⭐")
    print("3. Yahoo Finance (稳定可靠) ⭐")
    print("4. Vanguard CSV (官方CSV数据)")
    print("5. Vanguard官网 (官方网站)")
    print("6. ETF.com (第三方数据)")
    print("7. Morningstar (专业数据)")
    print("8. Selenium Morningstar (Selenium版本)")
    print("9. Selenium ETF.com (Selenium版本)")
    print("10. Vanguard API (官方API)")
    print("11. 简单API (备用数据源)")
    print("0. 自动模式 (按优先级尝试所有数据源，默认)")
    print("-" * 40)
    print("💡 推荐: 数据源2(最准确) 或 数据源3(最快)")

def get_user_choice(prompt, valid_choices):
    """获取用户选择"""
    while True:
        try:
            choice = input(f"{prompt}: ").strip()
            if choice in valid_choices:
                return choice
            else:
                print(f"❌ 请输入有效选择: {', '.join(valid_choices)}")
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            return None

def query_single_etf():
    """查询单个ETF"""
    print("\n🔍 查询单个ETF")
    print("-" * 30)
    
    # 获取ETF代码
    while True:
        symbol = input("请输入ETF代码 (如: SPY, QQQ, VTI): ").strip().upper()
        if symbol:
            break
        print("❌ 请输入有效的ETF代码")
    
    # 选择数据源
    show_data_source_menu()
    source_choice = get_user_choice("请选择数据源 (0-11)", 
                                  [str(i) for i in range(12)])
    if source_choice is None:
        return
    
    # 是否开启调试模式
    debug_choice = get_user_choice("是否开启调试模式? (y/n)", ['y', 'n', 'Y', 'N'])
    if debug_choice is None:
        return
    
    # 构建命令
    cmd = [sys.executable, 'etf_analyzer.py', symbol]
    if source_choice != '0':
        cmd.extend(['--source', source_choice])
    if debug_choice.lower() == 'y':
        cmd.append('--debug')
    
    print(f"\n🚀 正在查询 {symbol}...")
    subprocess.run(cmd)

def process_excel_file():
    """处理Excel/CSV文件"""
    print("\n📊 处理Excel/CSV文件")
    print("-" * 30)

    # 检查示例文件
    sample_files = [
        Path("input/美股ETF十大持仓.xlsx"),
        Path("input/核心ETF十大持仓 (2)(美股ETF).csv")
    ]

    available_samples = [f for f in sample_files if f.exists()]

    if available_samples:
        print("📁 发现示例文件:")
        for i, sample_file in enumerate(available_samples, 1):
            print(f"  {i}. {sample_file}")

        use_sample = get_user_choice("是否使用示例文件? (y/n)", ['y', 'n', 'Y', 'N'])
        if use_sample and use_sample.lower() == 'y':
            if len(available_samples) == 1:
                file_path = str(available_samples[0])
            else:
                choice = get_user_choice(f"选择文件 (1-{len(available_samples)})",
                                       [str(i) for i in range(1, len(available_samples) + 1)])
                if choice:
                    file_path = str(available_samples[int(choice) - 1])
                else:
                    return
        else:
            file_path = input("请输入Excel/CSV文件路径: ").strip()
    else:
        file_path = input("请输入Excel/CSV文件路径: ").strip()

    if not file_path or not os.path.exists(file_path):
        print("❌ 文件不存在")
        return
    
    # 选择数据源
    show_data_source_menu()
    source_choice = get_user_choice("请选择数据源 (0-11)", 
                                  [str(i) for i in range(12)])
    if source_choice is None:
        return
    
    # 是否开启调试模式
    debug_choice = get_user_choice("是否开启调试模式? (y/n)", ['y', 'n', 'Y', 'N'])
    if debug_choice is None:
        return
    
    # 构建命令
    cmd = [sys.executable, 'etf_analyzer.py', file_path]
    if source_choice != '0':
        cmd.extend(['--source', source_choice])
    if debug_choice.lower() == 'y':
        cmd.append('--debug')

    print(f"\n🚀 正在处理 {file_path}...")
    subprocess.run(cmd)

def show_help():
    """显示帮助信息"""
    print("\n📖 使用帮助")
    print("=" * 50)
    print("🎯 功能说明:")
    print("  • 查询美股ETF前十大持仓")
    print("  • 支持单个查询和批量处理")
    print("  • 多数据源保证数据获取成功率")
    print()
    print("📊 数据源说明:")
    print("  • 数据源2 (Vanguard Selenium): 官方数据，最准确")
    print("  • 数据源3 (Yahoo Finance): 快速稳定，推荐日常使用")
    print("  • 自动模式: 智能选择最佳可用数据源")
    print()
    print("📋 文件格式支持:")
    print("  • Excel文件 (.xlsx): 支持多个工作表")
    print("  • CSV文件 (.csv): 单个数据表")
    print("  • ETF代码应在单独的列中")
    print("  • 示例文件: input/美股ETF十大持仓.xlsx")
    print("  • 示例文件: input/核心ETF十大持仓 (2)(美股ETF).csv")
    print()
    print("🔧 常见问题:")
    print("  • 如果获取失败，尝试切换数据源")
    print("  • 开启调试模式可查看详细信息")
    print("  • 确保网络连接正常")
    print("=" * 50)

def main():
    """主函数"""
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"❌ 缺少依赖包: {', '.join(missing)}")
        install_choice = get_user_choice("是否自动安装? (y/n)", ['y', 'n', 'Y', 'N'])
        if install_choice and install_choice.lower() == 'y':
            if not install_dependencies():
                return
        else:
            print("请手动安装依赖包后再运行")
            return
    
    # 显示欢迎界面
    show_welcome()
    
    # 主循环
    while True:
        show_main_menu()
        choice = get_user_choice("请选择", ['1', '2', '3', '4', '5'])
        
        if choice is None or choice == '5':
            print("\n👋 感谢使用！")
            break
        elif choice == '1':
            query_single_etf()
        elif choice == '2':
            process_excel_file()
        elif choice == '3':
            show_data_source_menu()
            input("\n按回车键继续...")
        elif choice == '4':
            show_help()
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
