#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重试功能的脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from etf_analyzer import USETFAnalyzer

def test_retry_functionality():
    """测试重试功能"""
    print("🧪 测试重试功能")
    print("=" * 50)
    
    # 创建分析器实例
    analyzer = USETFAnalyzer(debug=True)
    
    # 测试一个可能失败的ETF代码，使用指定数据源和重试
    test_symbol = "SPY"
    source_choice = 3  # Yahoo Finance
    max_retries = 3
    
    print(f"测试ETF: {test_symbol}")
    print(f"数据源: {source_choice} (Yahoo Finance)")
    print(f"重试次数: {max_retries}")
    print("-" * 30)
    
    # 调用带重试的方法
    holdings = analyzer.get_us_etf_holdings(test_symbol, source_choice, max_retries)
    
    if holdings:
        print(f"\n✅ 成功获取到 {len(holdings)} 个持仓:")
        for i, holding in enumerate(holdings[:5], 1):  # 只显示前5个
            print(f"  {i}. {holding.get('symbol', 'N/A')} - {holding.get('name', 'N/A')} ({holding.get('weight', 'N/A')})")
    else:
        print("\n❌ 重试后仍然失败")
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    test_retry_functionality()
